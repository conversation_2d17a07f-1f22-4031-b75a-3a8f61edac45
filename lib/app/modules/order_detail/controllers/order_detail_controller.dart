import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../colors.dart';
import '../../../models/erp_category.dart';
import '../../../models/erp_order.dart';
import '../../../repositories/category_repository.dart';
import '../../../repositories/order_repository.dart';

enum TransactionType { expense, income, ignore }

class OrderDetailController extends GetxController with StateMixin<String> {
  final CategoryRepository _categoryRepository = Get.find<CategoryRepository>();
  final OrderRepository _orderRepository = Get.find<OrderRepository>();

  // 響應式變數
  final selectedTransactionType = TransactionType.expense.obs;
  final amount = '0'.obs;
  final selectedCategory = Rxn<ErpCategory>();
  final selectedDateTime = DateTime.now().obs;
  final note = ''.obs;
  final location = ''.obs;
  final latitude = Rxn<double>();
  final longitude = Rxn<double>();
  final categories = <ErpCategory>[].obs;
  final isLoading = false.obs;

  // 備註自動完成相關
  final noteSuggestions = <String>[].obs;
  final showNoteSuggestions = false.obs;
  final layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  // 儲存按鈕狀態管理
  final saveButtonState = ''.reactive;

  // 表單控制器
  final noteController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    saveButtonState.change('', status: RxStatus.success());
    _loadCategories();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    noteController.dispose();
    hideOverlay(); // 清理 overlay
    super.onClose();
  }

  // 載入分類列表
  Future<void> _loadCategories() async {
    try {
      isLoading.value = true;
      final categoryList = await _categoryRepository.getAllAsync();
      categories.value = categoryList;
    } catch (e) {
      Get.snackbar('錯誤', '載入分類失敗: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 設置交易類型
  void setTransactionType(TransactionType type) {
    selectedTransactionType.value = type;
  }

  // 數字鍵盤輸入
  void addDigit(String digit) {
    final currentValue = amount.value;

    // 如果是小數點，檢查是否已經有小數點
    if (digit == '.' && currentValue.contains('.')) {
      return;
    }

    // 如果當前值是 "0"，且輸入的不是小數點，則替換
    if (currentValue == '0' && digit != '.') {
      amount.value = digit;
    } else {
      amount.value = currentValue + digit;
    }
  }

  // 刪除數字
  void deleteDigit() {
    final currentValue = amount.value;

    if (currentValue.isNotEmpty) {
      final newValue = currentValue.substring(0, currentValue.length - 1);
      amount.value = newValue.isEmpty ? '0' : newValue;
    }
  }

  // 設置分類
  void setCategory(ErpCategory? category) {
    selectedCategory.value = category;
  }

  // 設置日期時間
  void setDateTime(DateTime dateTime) {
    selectedDateTime.value = dateTime;
  }

  // 設置備註
  void setNote(String noteText) {
    note.value = noteText;
    noteController.text = noteText;

    // 觸發自動完成搜尋
    if (noteText.trim().isNotEmpty) {
      _searchNoteSuggestions(noteText);
    } else {
      showNoteSuggestions.value = false;
      noteSuggestions.clear();
    }
  }

  // 搜尋備註建議
  Future<void> _searchNoteSuggestions(String query) async {
    try {
      if (query.trim().isEmpty) {
        showNoteSuggestions.value = false;
        noteSuggestions.clear();
        return;
      }

      final suggestions = await _orderRepository.searchNotesAsync(query, limit: 10);

      // 過濾掉與當前輸入完全相同的建議
      final filteredSuggestions = suggestions
          .where((suggestion) => suggestion.toLowerCase() != query.toLowerCase())
          .toList();

      noteSuggestions.value = filteredSuggestions;
      showNoteSuggestions.value = filteredSuggestions.isNotEmpty;
    } catch (e) {
      // 如果搜尋失敗，不顯示建議
      showNoteSuggestions.value = false;
      noteSuggestions.clear();
    }
  }

  // 選擇備註建議
  void selectNoteSuggestion(String suggestion) {
    noteController.text = suggestion;
    note.value = suggestion;
    showNoteSuggestions.value = false;
    noteSuggestions.clear();
  }

  // 隱藏備註建議
  void hideNoteSuggestions() {
    showNoteSuggestions.value = false;
  }

  // 公開的搜尋備註建議方法
  Future<void> searchNoteSuggestions(String query) async {
    await _searchNoteSuggestions(query);
  }

  // 顯示 Overlay
  void showOverlay(BuildContext context) {
    if (_overlayEntry != null) return;

    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: MediaQuery.of(context).size.width - 32, // 考慮左右邊距
        child: CompositedTransformFollower(
          link: layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 48), // 位於輸入框下方
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12),
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: isDark ? ErpColors.darkCardBackground : Colors.white,
                border: Border.all(
                  color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemCount: noteSuggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = noteSuggestions[index];
                  return InkWell(
                    onTap: () => selectNoteSuggestion(suggestion),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.history,
                            size: 16,
                            color: isDark
                                ? ErpColors.darkTextSecondary
                                : ErpColors.textSecondary,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              suggestion,
                              style: TextStyle(
                                fontSize: 14,
                                color: isDark
                                    ? ErpColors.darkTextPrimary
                                    : ErpColors.textPrimary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  // 隱藏 Overlay
  void hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // 設置地點
  void setLocation(String locationText) {
    location.value = locationText;
  }

  // 設置地理位置
  void setLocationCoordinates(double? lat, double? lng, String? locationName) {
    latitude.value = lat;
    longitude.value = lng;
    if (locationName != null && locationName.isNotEmpty) {
      location.value = locationName;
    }
  }

  // 清除地理位置
  void clearLocation() {
    latitude.value = null;
    longitude.value = null;
    location.value = '';
  }

  // 儲存交易
  Future<void> saveTransaction() async {
    try {
      // 驗證必填欄位
      if (amount.value == '0' || amount.value.isEmpty) {
        Get.snackbar('錯誤', '請輸入金額');
        return;
      }

      if (selectedCategory.value == null) {
        Get.snackbar('錯誤', '請選擇分類');
        return;
      }

      // 設置按鈕為載入狀態
      saveButtonState.change('', status: RxStatus.loading());
      isLoading.value = true;

      // 創建訂單對象
      final order = ErpOrder(
        type: _getTransactionTypeValue(),
        amount: double.tryParse(amount.value) ?? 0.0,
        note: note.value.isEmpty ? null : note.value,
        latitude: latitude.value,
        longitude: longitude.value,
        triggerAt: selectedDateTime.value,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 設置分類關聯
      order.parent.target = selectedCategory.value;

      // 儲存到資料庫
      await _orderRepository.addAsync(order);

      // 設置按鈕為成功狀態
      saveButtonState.change('', status: RxStatus.success());
      Get.snackbar('成功', '交易已儲存');
      Get.back();
    } catch (e) {
      // 設置按鈕為錯誤狀態
      saveButtonState.change(null, status: RxStatus.error('儲存失敗: $e'));
      Get.snackbar('錯誤', '儲存失敗: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 取消操作
  void cancel() {
    Get.back();
  }

  // 獲取交易類型數值
  int _getTransactionTypeValue() {
    switch (selectedTransactionType.value) {
      case TransactionType.expense:
        return 1; // 支出
      case TransactionType.income:
        return 2; // 收入
      case TransactionType.ignore:
        return 0; // 不計
    }
  }

  // 獲取交易類型顯示名稱
  String getTransactionTypeDisplayName(TransactionType type) {
    switch (type) {
      case TransactionType.expense:
        return '支出';
      case TransactionType.income:
        return '收入';
      case TransactionType.ignore:
        return '不計';
    }
  }

  // 獲取交易類型圖標
  IconData getTransactionTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.expense:
        return Icons.arrow_upward;
      case TransactionType.income:
        return Icons.arrow_downward;
      case TransactionType.ignore:
        return Icons.remove;
    }
  }
}
