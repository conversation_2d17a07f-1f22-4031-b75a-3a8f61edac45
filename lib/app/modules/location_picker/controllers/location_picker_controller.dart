import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';

class LocationPickerController extends GetxController {
  // 地圖控制器
  final MapController mapController = MapController();
  
  // 響應式變數
  final selectedLocation = Rxn<LatLng>();
  final currentZoom = 15.0.obs;
  final isLoading = false.obs;
  final locationName = ''.obs;
  
  // 台灣預設中心點（台北市）
  static const LatLng defaultCenter = LatLng(25.0330, 121.5654);
  
  @override
  void onInit() {
    super.onInit();
    
    // 如果有傳入初始位置，使用它；否則使用預設位置
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      final lat = arguments['latitude'] as double?;
      final lng = arguments['longitude'] as double?;
      final name = arguments['locationName'] as String?;
      
      if (lat != null && lng != null) {
        selectedLocation.value = LatLng(lat, lng);
        locationName.value = name ?? '';
      }
    }
    
    // 如果沒有初始位置，設置預設位置
    if (selectedLocation.value == null) {
      selectedLocation.value = defaultCenter;
    }
  }

  @override
  void onReady() {
    super.onReady();
    // 移動地圖到選定位置
    if (selectedLocation.value != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        mapController.move(selectedLocation.value!, currentZoom.value);
      });
    }
  }

  @override
  void onClose() {
    mapController.dispose();
    super.onClose();
  }

  // 處理地圖移動事件
  void onMapPositionChanged(MapCamera camera) {
    selectedLocation.value = camera.center;
    locationName.value = '${camera.center.latitude.toStringAsFixed(6)}, ${camera.center.longitude.toStringAsFixed(6)}';
  }

  // 確認選擇位置
  void confirmLocation() {
    if (selectedLocation.value != null) {
      Get.back(result: {
        'latitude': selectedLocation.value!.latitude,
        'longitude': selectedLocation.value!.longitude,
        'locationName': locationName.value.isNotEmpty 
            ? locationName.value 
            : '${selectedLocation.value!.latitude.toStringAsFixed(6)}, ${selectedLocation.value!.longitude.toStringAsFixed(6)}',
      });
    }
  }

  // 取消選擇
  void cancel() {
    Get.back();
  }

  // 縮放地圖
  void zoomIn() {
    final newZoom = (currentZoom.value + 1).clamp(1.0, 18.0);
    currentZoom.value = newZoom;
    try {
      mapController.move(selectedLocation.value ?? defaultCenter, newZoom);
    } catch (e) {
      // MapController 尚未初始化，忽略錯誤
    }
  }

  void zoomOut() {
    final newZoom = (currentZoom.value - 1).clamp(1.0, 18.0);
    currentZoom.value = newZoom;
    try {
      mapController.move(selectedLocation.value ?? defaultCenter, newZoom);
    } catch (e) {
      // MapController 尚未初始化，忽略錯誤
    }
  }

  // 移動到我的位置（預留功能）
  void moveToMyLocation() {
    // TODO: 實作定位功能
    Get.snackbar('提示', '定位功能尚未實現');
  }

  // 搜尋地點（預留功能）
  void searchLocation(String query) {
    // TODO: 實作地點搜尋功能
    Get.snackbar('提示', '搜尋功能尚未實現');
  }

  // 清除地理位置
  void clearLocation() {
    selectedLocation.value = null;
    locationName.value = '';
  }
}
