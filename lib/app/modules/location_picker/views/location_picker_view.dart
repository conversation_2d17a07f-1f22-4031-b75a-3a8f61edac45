import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';

import '../../../../colors.dart';
import '../controllers/location_picker_controller.dart';

class LocationPickerView extends GetView<LocationPickerController> {
  const LocationPickerView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? ErpColors.darkBackground : ErpColors.background,
      appBar: AppBar(
        title: const Text('選擇地點'),
        backgroundColor: isDark ? ErpColors.darkCardBackground : Colors.white,
        foregroundColor: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: controller.confirmLocation,
            child: Text(
              '確認',
              style: TextStyle(
                color: ErpColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // 地圖主體
          _buildMap(context),
          
          // 地點資訊卡片
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: _buildLocationInfoCard(context),
          ),
          
          // 地圖控制按鈕
          Positioned(
            right: 16,
            bottom: 160, // 增加距離底部的間距，避免被底部按鈕遮擋
            child: _buildMapControls(context),
          ),
          
          // 底部操作按鈕
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomActions(context),
          ),
        ],
      ),
    );
  }

  // 建立地圖
  Widget _buildMap(BuildContext context) {
    return Stack(
      children: [
        Obx(() => FlutterMap(
              mapController: controller.mapController,
              options: MapOptions(
                initialCenter: controller.selectedLocation.value ??
                    LocationPickerController.defaultCenter,
                initialZoom: controller.currentZoom.value,
                onPositionChanged: (camera, hasGesture) {
                  if (hasGesture) {
                    controller.onMapPositionChanged(camera);
                  }
                },
                interactionOptions: const InteractionOptions(
                  flags: InteractiveFlag.all,
                ),
              ),
              children: [
                // 地圖圖層 - 使用台灣國土測繪中心的 WMTS 服務
                TileLayer(
                  urlTemplate: 'https://wmts.nlsc.gov.tw/wmts/EMAP/default/GoogleMapsCompatible/{z}/{y}/{x}',
                  userAgentPackageName: 'com.example.pocket_trac',
                  maxZoom: 18,
                  minZoom: 1,
                ),
              ],
            )),

        // 中心標記 - 固定在地圖中心
        Center(
          child: Container(
            width: 40,
            height: 40,
            decoration: const BoxDecoration(
              color: Colors.transparent,
            ),
            child: const Icon(
              Icons.location_on,
              color: Colors.red,
              size: 40,
            ),
          ),
        ),
      ],
    );
  }

  // 建立地點資訊卡片
  Widget _buildLocationInfoCard(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Obx(() => Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: ErpColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '選定位置',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              if (controller.selectedLocation.value != null) ...[
                Text(
                  '緯度: ${controller.selectedLocation.value!.latitude.toStringAsFixed(6)}',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
                  ),
                ),
                Text(
                  '經度: ${controller.selectedLocation.value!.longitude.toStringAsFixed(6)}',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
                  ),
                ),
              ] else ...[
                Text(
                  '移動地圖選擇位置',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
                  ),
                ),
              ],
            ],
          ),
        ));
  }

  // 建立地圖控制按鈕
  Widget _buildMapControls(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      children: [
        // 放大按鈕
        Container(
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: controller.zoomIn,
            icon: const Icon(Icons.add),
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        // 縮小按鈕
        Container(
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: controller.zoomOut,
            icon: const Icon(Icons.remove),
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        // 定位按鈕
        Container(
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: controller.moveToMyLocation,
            icon: const Icon(Icons.my_location),
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
      ],
    );
  }

  // 建立底部操作按鈕
  Widget _buildBottomActions(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkCardBackground : Colors.white,
        border: Border(
          top: BorderSide(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: controller.cancel,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isDark ? ErpColors.darkSurface : const Color(0xFFF3F4F6),
                  foregroundColor: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Obx(() => ElevatedButton(
                    onPressed: controller.selectedLocation.value != null 
                        ? controller.confirmLocation 
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ErpColors.primary,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      '確認選擇',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
