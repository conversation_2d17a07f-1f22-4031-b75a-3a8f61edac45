import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';
import 'package:pocket_trac/app/modules/location_picker/controllers/location_picker_controller.dart';

void main() {
  group('LocationPickerController Tests', () {
    late LocationPickerController controller;

    setUp(() {
      Get.testMode = true;
      controller = LocationPickerController();
    });

    tearDown(() {
      Get.reset();
    });

    test('should initialize with default values', () {
      // Act
      controller.onInit();

      // Assert
      expect(controller.selectedLocation.value, isNotNull);
      expect(controller.currentZoom.value, equals(15.0));
      expect(controller.isLoading.value, isFalse);
      expect(controller.locationName.value, isEmpty);
    });

    test('should handle zoom in correctly', () {
      // Arrange
      controller.onInit();
      final initialZoom = controller.currentZoom.value;

      // Act
      controller.zoomIn();

      // Assert
      expect(controller.currentZoom.value, equals(initialZoom + 1));
    });

    test('should handle zoom out correctly', () {
      // Arrange
      controller.onInit();
      final initialZoom = controller.currentZoom.value;

      // Act
      controller.zoomOut();

      // Assert
      expect(controller.currentZoom.value, equals(initialZoom - 1));
    });

    test('should not zoom beyond limits', () {
      // Arrange
      controller.onInit();
      controller.currentZoom.value = 18.0; // Max zoom

      // Act
      controller.zoomIn();

      // Assert
      expect(controller.currentZoom.value, equals(18.0));

      // Arrange
      controller.currentZoom.value = 1.0; // Min zoom

      // Act
      controller.zoomOut();

      // Assert
      expect(controller.currentZoom.value, equals(1.0));
    });

    test('should clear location correctly', () {
      // Arrange
      controller.onInit();
      controller.selectedLocation.value = const LatLng(25.0330, 121.5654);
      controller.locationName.value = 'Test Location';

      // Act
      controller.clearLocation();

      // Assert
      expect(controller.selectedLocation.value, isNull);
      expect(controller.locationName.value, isEmpty);
    });
  });
}
