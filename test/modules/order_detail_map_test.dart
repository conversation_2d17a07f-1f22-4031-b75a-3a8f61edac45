import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'package:pocket_trac/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:pocket_trac/app/modules/order_detail/views/order_detail_view.dart';
import 'package:pocket_trac/app/repositories/category_repository.dart';
import 'package:pocket_trac/app/repositories/order_repository.dart';

class MockCategoryRepository extends GetxService implements CategoryRepository {
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

class MockOrderRepository extends GetxService implements OrderRepository {
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  group('OrderDetailView 地圖功能測試', () {
    late OrderDetailController controller;

    setUp(() {
      Get.testMode = true;
      Get.put<CategoryRepository>(MockCategoryRepository());
      Get.put<OrderRepository>(MockOrderRepository());
      controller = OrderDetailController();
      Get.put(controller);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('當沒有地理位置時，應該顯示添加地點按鈕', (WidgetTester tester) async {
      // 確保沒有地理位置資料
      controller.latitude.value = null;
      controller.longitude.value = null;
      controller.location.value = '';

      await tester.pumpWidget(
        GetMaterialApp(
          home: const OrderDetailView(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找添加地點按鈕
      expect(find.text('添加地點'), findsOneWidget);
      expect(find.byIcon(Icons.location_on), findsWidgets);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('當有地理位置時，應該顯示地圖', (WidgetTester tester) async {
      // 設置地理位置資料
      controller.latitude.value = 25.0330;
      controller.longitude.value = 121.5654;
      controller.location.value = '台北市';

      await tester.pumpWidget(
        GetMaterialApp(
          home: const OrderDetailView(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找地圖組件
      expect(find.byType(FlutterMap), findsOneWidget);
      expect(find.byType(TileLayer), findsOneWidget);
      expect(find.byType(MarkerLayer), findsOneWidget);
      
      // 查找地點資訊覆蓋層
      expect(find.text('台北市'), findsOneWidget);
      expect(find.byIcon(Icons.edit), findsOneWidget);
    });

    testWidgets('當有地理位置但沒有地點名稱時，應該顯示經緯度', (WidgetTester tester) async {
      // 設置地理位置資料但沒有地點名稱
      controller.latitude.value = 25.0330;
      controller.longitude.value = 121.5654;
      controller.location.value = '';

      await tester.pumpWidget(
        GetMaterialApp(
          home: const OrderDetailView(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找地圖組件
      expect(find.byType(FlutterMap), findsOneWidget);
      
      // 查找經緯度顯示
      expect(find.textContaining('25.0330'), findsOneWidget);
      expect(find.textContaining('121.5654'), findsOneWidget);
    });

    test('地理位置設置功能測試', () {
      // 測試設置地理位置
      controller.setLocationCoordinates(25.0330, 121.5654, '台北市');
      
      expect(controller.latitude.value, equals(25.0330));
      expect(controller.longitude.value, equals(121.5654));
      expect(controller.location.value, equals('台北市'));
    });

    test('清除地理位置功能測試', () {
      // 先設置地理位置
      controller.setLocationCoordinates(25.0330, 121.5654, '台北市');
      
      // 清除地理位置
      controller.clearLocation();
      
      expect(controller.latitude.value, isNull);
      expect(controller.longitude.value, isNull);
      expect(controller.location.value, equals(''));
    });

    testWidgets('地圖應該禁用交互功能', (WidgetTester tester) async {
      // 設置地理位置資料
      controller.latitude.value = 25.0330;
      controller.longitude.value = 121.5654;
      controller.location.value = '台北市';

      await tester.pumpWidget(
        GetMaterialApp(
          home: const OrderDetailView(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找 FlutterMap 組件
      final mapFinder = find.byType(FlutterMap);
      expect(mapFinder, findsOneWidget);

      // 獲取 FlutterMap widget
      final FlutterMap mapWidget = tester.widget(mapFinder);
      
      // 驗證交互選項設置為禁用所有交互
      expect(mapWidget.options.interactionOptions.flags, equals(InteractiveFlag.none));
    });

    testWidgets('地圖標記應該顯示在正確位置', (WidgetTester tester) async {
      const testLat = 25.0330;
      const testLng = 121.5654;
      
      // 設置地理位置資料
      controller.latitude.value = testLat;
      controller.longitude.value = testLng;
      controller.location.value = '台北市';

      await tester.pumpWidget(
        GetMaterialApp(
          home: const OrderDetailView(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找 MarkerLayer 組件
      final markerLayerFinder = find.byType(MarkerLayer);
      expect(markerLayerFinder, findsOneWidget);

      // 獲取 MarkerLayer widget
      final MarkerLayer markerLayer = tester.widget(markerLayerFinder);
      
      // 驗證標記位置
      expect(markerLayer.markers.length, equals(1));
      final marker = markerLayer.markers.first;
      expect(marker.point.latitude, equals(testLat));
      expect(marker.point.longitude, equals(testLng));
    });
  });
}
