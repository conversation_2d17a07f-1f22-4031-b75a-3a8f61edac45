// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in pocket_trac/test/modules/order_detail_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:mockito/mockito.dart' as _i1;
import 'package:pocket_trac/app/models/erp_category.dart' as _i6;
import 'package:pocket_trac/app/models/erp_order.dart' as _i9;
import 'package:pocket_trac/app/providers/box_provider.dart' as _i2;
import 'package:pocket_trac/app/repositories/category_repository.dart' as _i5;
import 'package:pocket_trac/app/repositories/order_repository.dart' as _i8;
import 'package:pocket_trac/objectbox.g.dart' as _i4;
import 'package:talker_flutter/talker_flutter.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeBoxProvider_0 extends _i1.SmartFake implements _i2.BoxProvider {
  _FakeBoxProvider_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTalker_1 extends _i1.SmartFake implements _i3.Talker {
  _FakeTalker_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBox_2<T> extends _i1.SmartFake implements _i4.Box<T> {
  _FakeBox_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStore_3 extends _i1.SmartFake implements _i4.Store {
  _FakeStore_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [CategoryRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockCategoryRepository extends _i1.Mock
    implements _i5.CategoryRepository {
  MockCategoryRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.BoxProvider get boxProvider => (super.noSuchMethod(
        Invocation.getter(#boxProvider),
        returnValue: _FakeBoxProvider_0(
          this,
          Invocation.getter(#boxProvider),
        ),
      ) as _i2.BoxProvider);

  @override
  _i3.Talker get talker => (super.noSuchMethod(
        Invocation.getter(#talker),
        returnValue: _FakeTalker_1(
          this,
          Invocation.getter(#talker),
        ),
      ) as _i3.Talker);

  @override
  _i4.Box<_i6.ErpCategory> get box => (super.noSuchMethod(
        Invocation.getter(#box),
        returnValue: _FakeBox_2<_i6.ErpCategory>(
          this,
          Invocation.getter(#box),
        ),
      ) as _i4.Box<_i6.ErpCategory>);

  @override
  _i7.Future<List<_i6.ErpCategory>> getAllAsync(
          {bool? includeDeleted = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllAsync,
          [],
          {#includeDeleted: includeDeleted},
        ),
        returnValue:
            _i7.Future<List<_i6.ErpCategory>>.value(<_i6.ErpCategory>[]),
      ) as _i7.Future<List<_i6.ErpCategory>>);

  @override
  _i7.Future<_i6.ErpCategory?> getByIdAsync(int? id) => (super.noSuchMethod(
        Invocation.method(
          #getByIdAsync,
          [id],
        ),
        returnValue: _i7.Future<_i6.ErpCategory?>.value(),
      ) as _i7.Future<_i6.ErpCategory?>);

  @override
  _i7.Future<_i6.ErpCategory?> getByObjectIdAsync(String? objectId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getByObjectIdAsync,
          [objectId],
        ),
        returnValue: _i7.Future<_i6.ErpCategory?>.value(),
      ) as _i7.Future<_i6.ErpCategory?>);

  @override
  _i7.Future<List<_i6.ErpCategory>> searchByNameAsync(String? name) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchByNameAsync,
          [name],
        ),
        returnValue:
            _i7.Future<List<_i6.ErpCategory>>.value(<_i6.ErpCategory>[]),
      ) as _i7.Future<List<_i6.ErpCategory>>);

  @override
  _i7.Future<int> saveAsync(_i6.ErpCategory? category) => (super.noSuchMethod(
        Invocation.method(
          #saveAsync,
          [category],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<int> addAsync(_i6.ErpCategory? category) => (super.noSuchMethod(
        Invocation.method(
          #addAsync,
          [category],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<int> updateAsync(_i6.ErpCategory? category) => (super.noSuchMethod(
        Invocation.method(
          #updateAsync,
          [category],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<List<int>> putManyAsync(List<_i6.ErpCategory>? categories) =>
      (super.noSuchMethod(
        Invocation.method(
          #putManyAsync,
          [categories],
        ),
        returnValue: _i7.Future<List<int>>.value(<int>[]),
      ) as _i7.Future<List<int>>);

  @override
  _i7.Future<bool> softDeleteAsync(int? id) => (super.noSuchMethod(
        Invocation.method(
          #softDeleteAsync,
          [id],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> restoreAsync(int? id) => (super.noSuchMethod(
        Invocation.method(
          #restoreAsync,
          [id],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> hardDeleteAsync(int? id) => (super.noSuchMethod(
        Invocation.method(
          #hardDeleteAsync,
          [id],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<int> deleteAllAsync() => (super.noSuchMethod(
        Invocation.method(
          #deleteAllAsync,
          [],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<int> countAsync({bool? includeDeleted = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #countAsync,
          [],
          {#includeDeleted: includeDeleted},
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Stream<List<_i6.ErpCategory>> watchAll() => (super.noSuchMethod(
        Invocation.method(
          #watchAll,
          [],
        ),
        returnValue: _i7.Stream<List<_i6.ErpCategory>>.empty(),
      ) as _i7.Stream<List<_i6.ErpCategory>>);

  @override
  _i7.Stream<List<_i6.ErpCategory>> watchActive() => (super.noSuchMethod(
        Invocation.method(
          #watchActive,
          [],
        ),
        returnValue: _i7.Stream<List<_i6.ErpCategory>>.empty(),
      ) as _i7.Stream<List<_i6.ErpCategory>>);

  @override
  _i7.Stream<List<_i6.ErpCategory>> watchByName(String? name) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchByName,
          [name],
        ),
        returnValue: _i7.Stream<List<_i6.ErpCategory>>.empty(),
      ) as _i7.Stream<List<_i6.ErpCategory>>);

  @override
  _i7.Stream<int> watchCount({bool? includeDeleted = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchCount,
          [],
          {#includeDeleted: includeDeleted},
        ),
        returnValue: _i7.Stream<int>.empty(),
      ) as _i7.Stream<int>);

  @override
  _i7.Stream<_i6.ErpCategory?> watchById(int? id) => (super.noSuchMethod(
        Invocation.method(
          #watchById,
          [id],
        ),
        returnValue: _i7.Stream<_i6.ErpCategory?>.empty(),
      ) as _i7.Stream<_i6.ErpCategory?>);

  @override
  _i7.Stream<_i6.ErpCategory?> watchByObjectId(String? objectId) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchByObjectId,
          [objectId],
        ),
        returnValue: _i7.Stream<_i6.ErpCategory?>.empty(),
      ) as _i7.Stream<_i6.ErpCategory?>);

  @override
  _i7.Stream<List<_i6.ErpCategory>> watchDeleted() => (super.noSuchMethod(
        Invocation.method(
          #watchDeleted,
          [],
        ),
        returnValue: _i7.Stream<List<_i6.ErpCategory>>.empty(),
      ) as _i7.Stream<List<_i6.ErpCategory>>);
}

/// A class which mocks [OrderRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockOrderRepository extends _i1.Mock implements _i8.OrderRepository {
  MockOrderRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.BoxProvider get boxProvider => (super.noSuchMethod(
        Invocation.getter(#boxProvider),
        returnValue: _FakeBoxProvider_0(
          this,
          Invocation.getter(#boxProvider),
        ),
      ) as _i2.BoxProvider);

  @override
  _i3.Talker get talker => (super.noSuchMethod(
        Invocation.getter(#talker),
        returnValue: _FakeTalker_1(
          this,
          Invocation.getter(#talker),
        ),
      ) as _i3.Talker);

  @override
  _i4.Store get store => (super.noSuchMethod(
        Invocation.getter(#store),
        returnValue: _FakeStore_3(
          this,
          Invocation.getter(#store),
        ),
      ) as _i4.Store);

  @override
  _i4.Box<_i9.ErpOrder> get box => (super.noSuchMethod(
        Invocation.getter(#box),
        returnValue: _FakeBox_2<_i9.ErpOrder>(
          this,
          Invocation.getter(#box),
        ),
      ) as _i4.Box<_i9.ErpOrder>);

  @override
  _i7.Future<int> addAsync(_i9.ErpOrder? order) => (super.noSuchMethod(
        Invocation.method(
          #addAsync,
          [order],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<_i9.ErpOrder?> getByIdAsync(int? id) => (super.noSuchMethod(
        Invocation.method(
          #getByIdAsync,
          [id],
        ),
        returnValue: _i7.Future<_i9.ErpOrder?>.value(),
      ) as _i7.Future<_i9.ErpOrder?>);

  @override
  _i7.Future<_i9.ErpOrder?> getByObjectIdAsync(String? objectId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getByObjectIdAsync,
          [objectId],
        ),
        returnValue: _i7.Future<_i9.ErpOrder?>.value(),
      ) as _i7.Future<_i9.ErpOrder?>);

  @override
  _i7.Future<void> updateAsync(_i9.ErpOrder? order) => (super.noSuchMethod(
        Invocation.method(
          #updateAsync,
          [order],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<int> saveAsync(_i9.ErpOrder? order) => (super.noSuchMethod(
        Invocation.method(
          #saveAsync,
          [order],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<List<_i9.ErpOrder>> getAllAsync({bool? includeDeleted = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllAsync,
          [],
          {#includeDeleted: includeDeleted},
        ),
        returnValue: _i7.Future<List<_i9.ErpOrder>>.value(<_i9.ErpOrder>[]),
      ) as _i7.Future<List<_i9.ErpOrder>>);

  @override
  _i7.Future<List<_i9.ErpOrder>> getOrdersAsync(_i8.OrderFilter? filter) =>
      (super.noSuchMethod(
        Invocation.method(
          #getOrdersAsync,
          [filter],
        ),
        returnValue: _i7.Future<List<_i9.ErpOrder>>.value(<_i9.ErpOrder>[]),
      ) as _i7.Future<List<_i9.ErpOrder>>);

  @override
  _i7.Future<bool> softDeleteAsync(int? id) => (super.noSuchMethod(
        Invocation.method(
          #softDeleteAsync,
          [id],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> restoreAsync(int? id) => (super.noSuchMethod(
        Invocation.method(
          #restoreAsync,
          [id],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> hardDeleteAsync(int? id) => (super.noSuchMethod(
        Invocation.method(
          #hardDeleteAsync,
          [id],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<int> countAsync({bool? includeDeleted = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #countAsync,
          [],
          {#includeDeleted: includeDeleted},
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Future<List<int>> putManyAsync(List<_i9.ErpOrder>? orders) =>
      (super.noSuchMethod(
        Invocation.method(
          #putManyAsync,
          [orders],
        ),
        returnValue: _i7.Future<List<int>>.value(<int>[]),
      ) as _i7.Future<List<int>>);

  @override
  _i7.Future<int> deleteAllAsync() => (super.noSuchMethod(
        Invocation.method(
          #deleteAllAsync,
          [],
        ),
        returnValue: _i7.Future<int>.value(0),
      ) as _i7.Future<int>);

  @override
  _i7.Stream<List<_i9.ErpOrder>> watchAll() => (super.noSuchMethod(
        Invocation.method(
          #watchAll,
          [],
        ),
        returnValue: _i7.Stream<List<_i9.ErpOrder>>.empty(),
      ) as _i7.Stream<List<_i9.ErpOrder>>);

  @override
  _i7.Stream<List<_i9.ErpOrder>> watchActive() => (super.noSuchMethod(
        Invocation.method(
          #watchActive,
          [],
        ),
        returnValue: _i7.Stream<List<_i9.ErpOrder>>.empty(),
      ) as _i7.Stream<List<_i9.ErpOrder>>);

  @override
  _i7.Stream<int> watchCount({bool? includeDeleted = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchCount,
          [],
          {#includeDeleted: includeDeleted},
        ),
        returnValue: _i7.Stream<int>.empty(),
      ) as _i7.Stream<int>);

  @override
  _i7.Stream<List<_i9.ErpOrder>> watchOrdersWithFilter(
          _i8.OrderFilter? filter) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchOrdersWithFilter,
          [filter],
        ),
        returnValue: _i7.Stream<List<_i9.ErpOrder>>.empty(),
      ) as _i7.Stream<List<_i9.ErpOrder>>);

  @override
  _i7.Stream<_i9.ErpOrder?> watchById(int? id) => (super.noSuchMethod(
        Invocation.method(
          #watchById,
          [id],
        ),
        returnValue: _i7.Stream<_i9.ErpOrder?>.empty(),
      ) as _i7.Stream<_i9.ErpOrder?>);

  @override
  _i7.Stream<_i9.ErpOrder?> watchByObjectId(String? objectId) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchByObjectId,
          [objectId],
        ),
        returnValue: _i7.Stream<_i9.ErpOrder?>.empty(),
      ) as _i7.Stream<_i9.ErpOrder?>);

  @override
  _i7.Future<List<String>> getDistinctNotesAsync({int? limit = 50}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getDistinctNotesAsync,
          [],
          {#limit: limit},
        ),
        returnValue: _i7.Future<List<String>>.value(<String>[]),
      ) as _i7.Future<List<String>>);

  @override
  _i7.Future<List<String>> searchNotesAsync(
    String? query, {
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchNotesAsync,
          [query],
          {#limit: limit},
        ),
        returnValue: _i7.Future<List<String>>.value(<String>[]),
      ) as _i7.Future<List<String>>);
}
