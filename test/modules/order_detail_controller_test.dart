import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:pocket_trac/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:pocket_trac/app/repositories/category_repository.dart';
import 'package:pocket_trac/app/repositories/order_repository.dart';
import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:pocket_trac/app/models/erp_order.dart';

import 'order_detail_controller_test.mocks.dart';

@GenerateMocks([CategoryRepository, OrderRepository])
void main() {
  group('OrderDetailController Tests', () {
    late OrderDetailController controller;
    late MockCategoryRepository mockCategoryRepository;
    late MockOrderRepository mockOrderRepository;

    setUp(() {
      // 初始化 GetX
      Get.testMode = true;
      
      // 創建 mock 對象
      mockCategoryRepository = MockCategoryRepository();
      mockOrderRepository = MockOrderRepository();
      
      // 註冊 mock 依賴
      Get.put<CategoryRepository>(mockCategoryRepository);
      Get.put<OrderRepository>(mockOrderRepository);
      
      // 創建控制器
      controller = OrderDetailController();
    });

    tearDown(() {
      Get.reset();
    });

    group('Initialization', () {
      test('should initialize with default values', () {
        expect(controller.selectedTransactionType.value, equals(TransactionType.expense));
        expect(controller.amount.value, equals('0'));
        expect(controller.selectedCategory.value, isNull);
        expect(controller.note.value, equals(''));
        expect(controller.location.value, equals(''));
        expect(controller.latitude.value, isNull);
        expect(controller.longitude.value, isNull);
        expect(controller.categories.value, isEmpty);
        expect(controller.isLoading.value, isFalse);
        expect(controller.noteSuggestions.value, isEmpty);
        expect(controller.showNoteSuggestions.value, isFalse);
      });
    });

    group('Transaction Type Management', () {
      test('should set transaction type', () {
        controller.setTransactionType(TransactionType.income);
        expect(controller.selectedTransactionType.value, equals(TransactionType.income));
        
        controller.setTransactionType(TransactionType.ignore);
        expect(controller.selectedTransactionType.value, equals(TransactionType.ignore));
      });
    });

    group('Amount Management', () {
      test('should add digits correctly', () {
        controller.addDigit('1');
        expect(controller.amount.value, equals('1'));
        
        controller.addDigit('2');
        expect(controller.amount.value, equals('12'));
        
        controller.addDigit('3');
        expect(controller.amount.value, equals('123'));
      });

      test('should handle decimal point correctly', () {
        controller.addDigit('1');
        controller.addDigit('.');
        controller.addDigit('5');
        expect(controller.amount.value, equals('1.5'));
        
        // 不應該允許多個小數點
        controller.addDigit('.');
        expect(controller.amount.value, equals('1.5'));
      });

      test('should replace initial zero', () {
        expect(controller.amount.value, equals('0'));
        controller.addDigit('5');
        expect(controller.amount.value, equals('5'));
      });

      test('should not replace zero when adding decimal point', () {
        expect(controller.amount.value, equals('0'));
        controller.addDigit('.');
        expect(controller.amount.value, equals('0.'));
      });

      test('should delete digits correctly', () {
        controller.addDigit('1');
        controller.addDigit('2');
        controller.addDigit('3');
        expect(controller.amount.value, equals('123'));
        
        controller.deleteDigit();
        expect(controller.amount.value, equals('12'));
        
        controller.deleteDigit();
        expect(controller.amount.value, equals('1'));
        
        controller.deleteDigit();
        expect(controller.amount.value, equals('0'));
        
        // 再次刪除應該保持為 '0'
        controller.deleteDigit();
        expect(controller.amount.value, equals('0'));
      });
    });

    group('Category Management', () {
      test('should set category', () {
        final category = ErpCategory(name: '測試分類', color: '#FF0000', icon: 'test');
        controller.setCategory(category);
        expect(controller.selectedCategory.value, equals(category));
      });

      test('should clear category', () {
        final category = ErpCategory(name: '測試分類', color: '#FF0000', icon: 'test');
        controller.setCategory(category);
        controller.setCategory(null);
        expect(controller.selectedCategory.value, isNull);
      });
    });

    group('DateTime Management', () {
      test('should set date time', () {
        final testDateTime = DateTime(2024, 1, 15, 10, 30);
        controller.setDateTime(testDateTime);
        expect(controller.selectedDateTime.value, equals(testDateTime));
      });
    });

    group('Note Management', () {
      test('should set note', () {
        controller.setNote('測試備註');
        expect(controller.note.value, equals('測試備註'));
        expect(controller.noteController.text, equals('測試備註'));
      });

      test('should clear note suggestions when note is empty', () {
        controller.setNote('');
        expect(controller.showNoteSuggestions.value, isFalse);
        expect(controller.noteSuggestions.value, isEmpty);
      });

      test('should select note suggestion', () {
        controller.selectNoteSuggestion('建議備註');
        expect(controller.noteController.text, equals('建議備註'));
        expect(controller.note.value, equals('建議備註'));
        expect(controller.showNoteSuggestions.value, isFalse);
        expect(controller.noteSuggestions.value, isEmpty);
      });

      test('should hide note suggestions', () {
        controller.hideNoteSuggestions();
        expect(controller.showNoteSuggestions.value, isFalse);
      });
    });

    group('Note Autocomplete', () {
      test('should search note suggestions successfully', () async {
        // Arrange
        final mockSuggestions = ['咖啡店消費', '咖啡豆購買'];
        when(mockOrderRepository.searchNotesAsync('咖啡', limit: 10))
            .thenAnswer((_) async => mockSuggestions);

        // Act
        await controller.searchNoteSuggestions('咖啡');

        // Assert
        expect(controller.noteSuggestions.value, equals(mockSuggestions));
        expect(controller.showNoteSuggestions.value, isTrue);
        verify(mockOrderRepository.searchNotesAsync('咖啡', limit: 10)).called(1);
      });

      test('should filter out exact matches from suggestions', () async {
        // Arrange
        final mockSuggestions = ['咖啡店消費', '咖啡', '咖啡豆購買'];
        when(mockOrderRepository.searchNotesAsync('咖啡', limit: 10))
            .thenAnswer((_) async => mockSuggestions);

        // Act
        await controller.searchNoteSuggestions('咖啡');

        // Assert
        expect(controller.noteSuggestions.value, equals(['咖啡店消費', '咖啡豆購買']));
        expect(controller.showNoteSuggestions.value, isTrue);
      });

      test('should handle empty search query', () async {
        // Act
        await controller.searchNoteSuggestions('');

        // Assert
        expect(controller.showNoteSuggestions.value, isFalse);
        expect(controller.noteSuggestions.value, isEmpty);
        verifyNever(mockOrderRepository.searchNotesAsync(any, limit: anyNamed('limit')));
      });

      test('should handle search error gracefully', () async {
        // Arrange
        when(mockOrderRepository.searchNotesAsync('咖啡', limit: 10))
            .thenThrow(Exception('搜尋失敗'));

        // Act
        await controller.searchNoteSuggestions('咖啡');

        // Assert
        expect(controller.showNoteSuggestions.value, isFalse);
        expect(controller.noteSuggestions.value, isEmpty);
      });
    });

    group('Location Management', () {
      test('should set location', () {
        controller.setLocation('測試地點');
        expect(controller.location.value, equals('測試地點'));
      });

      test('should set location coordinates', () {
        controller.setLocationCoordinates(25.0330, 121.5654, '台北101');
        expect(controller.latitude.value, equals(25.0330));
        expect(controller.longitude.value, equals(121.5654));
        expect(controller.location.value, equals('台北101'));
      });

      test('should set coordinates without location name', () {
        controller.setLocationCoordinates(25.0330, 121.5654, null);
        expect(controller.latitude.value, equals(25.0330));
        expect(controller.longitude.value, equals(121.5654));
        expect(controller.location.value, equals(''));
      });

      test('should clear location', () {
        controller.setLocationCoordinates(25.0330, 121.5654, '台北101');
        controller.clearLocation();
        expect(controller.latitude.value, isNull);
        expect(controller.longitude.value, isNull);
        expect(controller.location.value, equals(''));
      });
    });
  });
}
